"""
ES监控系统测试脚本
"""

import json
import logging
from datetime import datetime, timedelta
from es_monitor import ESMonitor
from data_analyzer import DataAnalyzer
from report_generator import ReportGenerator


def test_config_loading():
    """测试配置文件加载"""
    print("🔧 测试配置文件加载...")
    try:
        monitor = ESMonitor("config.json")
        config = monitor.config
        
        print(f"✅ ES主机: {config['elasticsearch']['host']}")
        print(f"✅ 索引数量: {len(config['indices'])}")
        print(f"✅ 门限值: {config['monitoring']['threshold_value']}%")
        print(f"✅ 监控期间: {config['monitoring']['start_date']} 到 {config['monitoring']['end_date']}")
        
        return True
    except Exception as e:
        print(f"❌ 配置加载失败: {e}")
        return False


def test_es_connection():
    """测试ES连接"""
    print("\n🔗 测试ES连接...")
    try:
        monitor = ESMonitor("config.json")
        if monitor.test_connection():
            print("✅ ES连接成功")
            return True
        else:
            print("❌ ES连接失败")
            return False
    except Exception as e:
        print(f"❌ ES连接测试出错: {e}")
        return False


def test_data_analyzer():
    """测试数据分析器"""
    print("\n📊 测试数据分析器...")
    try:
        # 创建测试数据
        test_data = [
            {
                'TryDate': '2024-01-01',
                'TryTime': '10:00:00',
                'IP': '***********',
                'Version': '1.0',
                'cpu_usage': 50.0
            },
            {
                'TryDate': '2024-01-02',
                'TryTime': '10:00:00',
                'IP': '***********',
                'Version': '1.0',
                'cpu_usage': 60.0  # 20% 增长，超过5%门限
            },
            {
                'TryDate': '2024-01-03',
                'TryTime': '10:00:00',
                'IP': '***********',
                'Version': '1.0',
                'cpu_usage': 61.0  # 1.67% 增长，未超过门限
            }
        ]
        
        config = {
            'monitoring': {'threshold_value': 5.0},
            'indices': {
                'test-index': {
                    'fields': ['cpu_usage']
                }
            }
        }
        
        analyzer = DataAnalyzer(config)
        anomalies = analyzer.analyze_field_trends('test-index', 'cpu_usage', test_data)
        
        print(f"✅ 检测到 {len(anomalies)} 个异常")
        if anomalies:
            for anomaly in anomalies:
                print(f"   - 变化率: {anomaly['ChangeRate']}%")
        
        return True
    except Exception as e:
        print(f"❌ 数据分析测试失败: {e}")
        return False


def test_report_generation():
    """测试报告生成"""
    print("\n📋 测试报告生成...")
    try:
        config = {
            'monitoring': {
                'start_date': '2024-01-01',
                'end_date': '2024-01-03',
                'threshold_value': 5.0
            },
            'output': {
                'html_filename_prefix': 'test_report'
            }
        }
        
        # 创建测试异常数据
        test_anomalies = {
            'test-index-cpu_usage': [
                {
                    'Index': 'test-index',
                    'Field': 'cpu_usage',
                    'PrevDate': '2024-01-01',
                    'PrevTime': '10:00:00',
                    'PrevIP': '***********',
                    'PrevVersion': '1.0',
                    'PrevValue': 50.0,
                    'CurrDate': '2024-01-02',
                    'CurrTime': '10:00:00',
                    'CurrIP': '***********',
                    'CurrVersion': '1.0',
                    'CurrValue': 60.0,
                    'ChangeRate': 20.0,
                    'Threshold': 5.0
                }
            ]
        }
        
        test_summary = {
            'total_fields_analyzed': 1,
            'fields_with_anomalies': 1,
            'total_anomalies': 1,
            'threshold_used': 5.0
        }
        
        generator = ReportGenerator(config)
        html_file = generator.generate_html_report(test_anomalies, test_summary)
        
        print(f"✅ 测试报告已生成: {html_file}")
        return True
    except Exception as e:
        print(f"❌ 报告生成测试失败: {e}")
        return False


def run_full_test():
    """运行完整测试"""
    print("🚀 开始ES监控系统测试\n")
    
    tests = [
        ("配置加载", test_config_loading),
        ("ES连接", test_es_connection),
        ("数据分析", test_data_analyzer),
        ("报告生成", test_report_generation)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
    
    print(f"\n📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！系统可以正常使用。")
    else:
        print("⚠️  部分测试失败，请检查配置和环境。")
    
    return passed == total


if __name__ == "__main__":
    # 设置日志级别
    logging.getLogger().setLevel(logging.WARNING)
    
    success = run_full_test()
    
    if success:
        print("\n💡 提示: 运行 'python main.py' 开始正式监控")
    else:
        print("\n💡 提示: 请先解决测试中发现的问题")
