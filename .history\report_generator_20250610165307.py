"""
报告生成模块
负责生成HTML格式的监控报告
"""

import os
import logging
from datetime import datetime
from typing import Dict, List, Any
from jinja2 import Template


class ReportGenerator:
    """报告生成器"""
    
    def __init__(self, config: Dict[str, Any]):
        """初始化报告生成器"""
        self.config = config
        self.logger = self._setup_logger()
    
    def _setup_logger(self) -> logging.Logger:
        """设置日志"""
        logger = logging.getLogger('report_generator')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def generate_html_report(self, anomaly_lists: Dict[str, List[Dict[str, Any]]],
                           summary_stats: Dict[str, Any],
                           actual_start_date: str = None,
                           actual_end_date: str = None) -> str:
        """
        生成HTML报告
        
        Args:
            anomaly_lists: 异常数据列表
            summary_stats: 摘要统计
            
        Returns:
            生成的HTML文件路径
        """
        # 生成文件名
        current_date = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename_prefix = self.config['output']['html_filename_prefix']
        html_filename = f"{filename_prefix}_{current_date}.html"
        
        # 生成HTML内容
        html_content = self._create_html_content(anomaly_lists, summary_stats, actual_start_date, actual_end_date)
        
        # 写入文件
        try:
            with open(html_filename, 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            self.logger.info(f"HTML报告已生成: {html_filename}")
            return html_filename
            
        except Exception as e:
            self.logger.error(f"生成HTML报告失败: {e}")
            raise
    
    def _create_html_content(self, anomaly_lists: Dict[str, List[Dict[str, Any]]],
                           summary_stats: Dict[str, Any],
                           actual_start_date: str = None,
                           actual_end_date: str = None) -> str:
        """创建HTML内容"""
        
        html_template = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ES监控报告 - {{ report_date }}</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .header {
            background-color: #2c3e50;
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .summary {
            background-color: #ecf0f1;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .summary-item {
            display: inline-block;
            margin-right: 30px;
            font-weight: bold;
        }
        .controls {
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .control-group {
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            flex-wrap: wrap;
        }
        .control-group label {
            display: inline-block;
            width: 80px;
            font-weight: bold;
            margin-right: 5px;
        }
        .control-group input, .control-group select {
            padding: 5px;
            margin-right: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .control-group select {
            max-width: 150px;
        }
        .control-group button {
            padding: 8px 15px;
            background-color: #3498db;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .control-group button:hover {
            background-color: #2980b9;
        }
        .section {
            background-color: white;
            margin-bottom: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .section-header {
            background-color: #3498db;
            color: white;
            padding: 15px;
            border-radius: 8px 8px 0 0;
            font-weight: bold;
            font-size: 16px;
        }
        .table-container {
            padding: 15px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            font-size: 14px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f8f9fa;
            font-weight: bold;
            cursor: pointer;
            position: relative;
        }
        th:hover {
            background-color: #e9ecef;
        }
        th.sortable::after {
            content: ' ↕';
            color: #999;
        }
        th.sort-asc::after {
            content: ' ↑';
            color: #333;
        }
        th.sort-desc::after {
            content: ' ↓';
            color: #333;
        }
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        .change-rate {
            font-weight: bold;
        }
        .change-rate.positive {
            color: #e74c3c;
        }
        .change-rate.negative {
            color: #27ae60;
        }
        .no-data {
            text-align: center;
            color: #7f8c8d;
            font-style: italic;
            padding: 20px;
        }
        .hidden {
            display: none;
        }
        .tabs {
            display: flex;
            background-color: #f8f9fa;
            border-bottom: 2px solid #3498db;
            margin-bottom: 20px;
        }
        .tab {
            padding: 15px 25px;
            cursor: pointer;
            background-color: #ecf0f1;
            border: none;
            border-bottom: 3px solid transparent;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        .tab:hover {
            background-color: #d5dbdb;
        }
        .tab.active {
            background-color: white;
            border-bottom: 3px solid #3498db;
            color: #3498db;
        }
        .tab-content {
            display: none;
        }
        .tab-content.active {
            display: block;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Elasticsearch 监控报告</h1>
        <p>生成时间: {{ report_date }}</p>
        <p>监控期间: {{ start_date }} 至 {{ end_date }}</p>
    </div>
    
    <div class="summary">
        <h2>摘要统计</h2>
        <div class="summary-item">分析字段总数: {{ summary_stats.total_fields_analyzed }}</div>
        <div class="summary-item">有异常的字段数: {{ summary_stats.fields_with_anomalies }}</div>
        <div class="summary-item">异常记录总数: {{ summary_stats.total_anomalies }}</div>
        <div class="summary-item">门限值: {{ summary_stats.threshold_used }}%</div>
    </div>

    <div class="controls">
        <h3>过滤和排序控制</h3>
        <div class="control-group">
            <label>开始时间:</label>
            <input type="date" id="filterStartDate" placeholder="开始日期">
            <label>结束时间:</label>
            <input type="date" id="filterEndDate" placeholder="结束日期">
        </div>
        <div class="control-group">
            <label>过滤IP:</label>
            <select id="filterIP" multiple>
                <option value="">全部IP</option>
            </select>
            <label>过滤版本:</label>
            <select id="filterVersion" multiple>
                <option value="">全部版本</option>
            </select>
        </div>
        <div class="control-group">
            <button onclick="applyFilters()">应用过滤</button>
            <button onclick="clearFilters()">清除过滤</button>
        </div>
        <div class="control-group">
            <label>显示列:</label>
            <label><input type="checkbox" id="col-starttime" checked> 开始时间</label>
            <label><input type="checkbox" id="col-endtime" checked> 结束时间</label>
            <label><input type="checkbox" id="col-ip" checked> IP</label>
            <label><input type="checkbox" id="col-owner" checked> Owner</label>
            <label><input type="checkbox" id="col-version" checked> 版本信息</label>
            <label><input type="checkbox" id="col-startvalue" checked> 开始数值</label>
            <label><input type="checkbox" id="col-endvalue" checked> 结束数值</label>
            <label><input type="checkbox" id="col-changerate" checked> 变化率</label>
            <button onclick="applyColumnVisibility()">应用列设置</button>
        </div>
    </div>
    
    {% if anomaly_lists %}
        {% for list_name, anomalies in anomaly_lists.items() %}
        <div class="section">
            <div class="section-header">
                {{ list_name }} ({{ anomalies|length }} 个异常)
            </div>
            <div class="table-container">
                <table>
                    <thead>
                        <tr>
                            <th class="sortable col-starttime" onclick="sortTable(this, 0)">开始时间</th>
                            <th class="sortable col-endtime" onclick="sortTable(this, 1)">结束时间</th>
                            <th class="sortable col-ip" onclick="sortTable(this, 2)">IP</th>
                            <th class="sortable col-owner" onclick="sortTable(this, 3)">Owner</th>
                            <th class="sortable col-version" onclick="sortTable(this, 4)">版本信息</th>
                            <th class="sortable col-startvalue" onclick="sortTable(this, 5)">开始数值</th>
                            <th class="sortable col-endvalue" onclick="sortTable(this, 6)">结束数值</th>
                            <th class="sortable col-changerate" onclick="sortTable(this, 7)">变化率(%)</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for anomaly in anomalies %}
                        <tr class="data-row">
                            <td class="col-starttime">{{ anomaly.StartTime }}</td>
                            <td class="col-endtime">{{ anomaly.EndTime }}</td>
                            <td class="col-ip">{{ anomaly.IP }}</td>
                            <td class="col-owner">{{ anomaly.Owner }}</td>
                            <td class="col-version">{{ anomaly.VersionInfo }}</td>
                            <td class="col-startvalue">{{ anomaly.StartValue }}</td>
                            <td class="col-endvalue">{{ anomaly.EndValue }}</td>
                            <td class="col-changerate change-rate positive">
                                {{ anomaly.ChangeRate }}%
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
        {% endfor %}
    {% else %}
        <div class="section">
            <div class="section-header">监控结果</div>
            <div class="no-data">
                在指定的监控期间内，所有字段的变化都在正常范围内，未检测到超过门限值的异常。
            </div>
        </div>
    {% endif %}
    
    <div style="margin-top: 30px; text-align: center; color: #7f8c8d; font-size: 12px;">
        <p>报告由 ES Monitor 系统自动生成</p>
    </div>

    <script>
        // 排序功能
        function sortTable(header, columnIndex) {
            const table = header.closest('table');
            const tbody = table.querySelector('tbody');
            const rows = Array.from(tbody.querySelectorAll('tr.data-row'));

            // 确定排序方向
            let isAsc = header.classList.contains('sort-desc');

            // 清除所有排序标记
            table.querySelectorAll('th').forEach(th => {
                th.classList.remove('sort-asc', 'sort-desc');
            });

            // 设置当前列的排序标记
            header.classList.add(isAsc ? 'sort-asc' : 'sort-desc');

            // 排序行
            rows.sort((a, b) => {
                const aVal = a.cells[columnIndex].textContent.trim();
                const bVal = b.cells[columnIndex].textContent.trim();

                // 尝试数值比较
                const aNum = parseFloat(aVal.replace('%', ''));
                const bNum = parseFloat(bVal.replace('%', ''));

                if (!isNaN(aNum) && !isNaN(bNum)) {
                    return isAsc ? aNum - bNum : bNum - aNum;
                } else {
                    return isAsc ? aVal.localeCompare(bVal) : bVal.localeCompare(aVal);
                }
            });

            // 重新插入排序后的行
            rows.forEach(row => tbody.appendChild(row));
        }

        // 过滤功能
        function applyFilters() {
            const startDateFilter = document.getElementById('filterStartDate').value;
            const endDateFilter = document.getElementById('filterEndDate').value;
            const ipFilter = Array.from(document.getElementById('filterIP').selectedOptions).map(option => option.value);
            const versionFilter = Array.from(document.getElementById('filterVersion').selectedOptions).map(option => option.value);

            document.querySelectorAll('tr.data-row').forEach(row => {
                const startTime = row.querySelector('.col-starttime').textContent;
                const endTime = row.querySelector('.col-endtime').textContent;
                const ip = row.querySelector('.col-ip').textContent;
                const version = row.querySelector('.col-version').textContent;

                // 日期过滤
                let dateMatch = true;
                if (startDateFilter) {
                    const rowStartDate = startTime.split(' ')[0];
                    dateMatch = dateMatch && rowStartDate >= startDateFilter;
                }
                if (endDateFilter) {
                    const rowEndDate = endTime.split(' ')[0];
                    dateMatch = dateMatch && rowEndDate <= endDateFilter;
                }

                // IP过滤
                const ipMatch = ipFilter.length === 0 || ipFilter.includes('') || ipFilter.includes(ip);

                // 版本过滤
                const versionMatch = versionFilter.length === 0 || versionFilter.includes('') ||
                                   versionFilter.some(v => version.includes(v));

                if (dateMatch && ipMatch && versionMatch) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        }

        // 清除过滤
        function clearFilters() {
            document.getElementById('filterStartDate').value = '';
            document.getElementById('filterEndDate').value = '';
            document.getElementById('filterIP').selectedIndex = -1;
            document.getElementById('filterVersion').selectedIndex = -1;
            document.querySelectorAll('tr.data-row').forEach(row => {
                row.style.display = '';
            });
        }

        // 列显示控制
        function applyColumnVisibility() {
            const columns = ['starttime', 'endtime', 'ip', 'owner', 'version', 'startvalue', 'endvalue', 'changerate'];

            columns.forEach((col, index) => {
                const checkbox = document.getElementById(`col-${col}`);
                const isVisible = checkbox.checked;

                // 控制表头
                document.querySelectorAll(`.col-${col}`).forEach(element => {
                    element.style.display = isVisible ? '' : 'none';
                });
            });
        }

        // 初始化下拉列表
        function initializeDropdowns() {
            const ipSet = new Set();
            const versionSet = new Set();

            document.querySelectorAll('tr.data-row').forEach(row => {
                const ip = row.querySelector('.col-ip').textContent.trim();
                const version = row.querySelector('.col-version').textContent.trim();

                if (ip) ipSet.add(ip);
                if (version) versionSet.add(version);
            });

            // 填充IP下拉列表
            const ipSelect = document.getElementById('filterIP');
            ipSet.forEach(ip => {
                const option = document.createElement('option');
                option.value = ip;
                option.textContent = ip;
                ipSelect.appendChild(option);
            });

            // 填充版本下拉列表
            const versionSelect = document.getElementById('filterVersion');
            versionSet.forEach(version => {
                const option = document.createElement('option');
                option.value = version;
                option.textContent = version;
                versionSelect.appendChild(option);
            });
        }

        // 页面加载时的初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化下拉列表
            initializeDropdowns();

            // 为过滤控件添加事件监听
            document.getElementById('filterStartDate').addEventListener('change', applyFilters);
            document.getElementById('filterEndDate').addEventListener('change', applyFilters);
            document.getElementById('filterIP').addEventListener('change', applyFilters);
            document.getElementById('filterVersion').addEventListener('change', applyFilters);
        });
    </script>
</body>
</html>
        """
        
        template = Template(html_template)
        
        # 使用实际日期或配置中的日期
        display_start_date = actual_start_date or self.config['monitoring']['start_date']
        display_end_date = actual_end_date or self.config['monitoring']['end_date']

        return template.render(
            report_date=datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            start_date=display_start_date,
            end_date=display_end_date,
            anomaly_lists=anomaly_lists,
            summary_stats=summary_stats
        )
