{"elasticsearch": {"host": "http://***************:9200"}, "indices": {"psis-collector-cpu-index": {"fields": ["cpu_usage", "mem_usage", "cpu_kmalloc_512_active_objs", "cpu_kmalloc_512_num_objs", "cpu_kmalloc_256_active_objs", "cpu_kmalloc_256_num_objs", "cpu_kmalloc_128_active_objs", "cpu_kmalloc_128_num_objs"]}, "psis-collector-harddisk-index": {"fields": ["mmcblk0p1_Avail", "mmcblk0p2_Avail", "mmcblk0p3_Avail", "mmcblk0p4_Avail", "tmpfs_sysfscgroupUsed", "devtmpfs_devUsage", "tmpfs_runUsage", "tmpfs_tmpUsage", "tmpfs_sysfscgroupUsage", "tmpfs_runlockUsage", "rootUsage"]}}, "monitoring": {"threshold_value": 2.0, "start_date": "2025-01-01", "end_date": "2025-06-10"}, "threshold_monitoring": {"psis-collector-cpu-index": {"cpu_usage": 0.7, "mem_usage": 50, "process_max_cpu_use": 0.9}, "psis-collector-harddisk-index": {"mmcblk0p1_usage": 0.8, "mmcblk0p2_usage": 0.8, "mmcblk0p3_Usage": 0.8, "mmcblk0p4_Usage": 0.8, "tmpfs_sysfscgroupUsage": 0.8, "devtmpfs_devUsage": 0.8, "tmpfs_runUsage": 0.8, "tmpfs_tmpUsage": 0.8, "tmpfs_runlockUsage": 0.8, "rootUsage": 0.8}}, "output": {"html_filename_prefix": "es_monitor_report"}}