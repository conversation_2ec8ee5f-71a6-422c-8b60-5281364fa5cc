"""
报告生成模块
负责生成HTML格式的监控报告
"""

import os
import logging
from datetime import datetime
from typing import Dict, List, Any
from jinja2 import Template


class ReportGenerator:
    """报告生成器"""
    
    def __init__(self, config: Dict[str, Any]):
        """初始化报告生成器"""
        self.config = config
        self.logger = self._setup_logger()
    
    def _setup_logger(self) -> logging.Logger:
        """设置日志"""
        logger = logging.getLogger('report_generator')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def generate_html_report(self, anomaly_lists: Dict[str, List[Dict[str, Any]]],
                           summary_stats: Dict[str, Any],
                           actual_start_date: str = None,
                           actual_end_date: str = None) -> str:
        """
        生成HTML报告
        
        Args:
            anomaly_lists: 异常数据列表
            summary_stats: 摘要统计
            
        Returns:
            生成的HTML文件路径
        """
        # 生成文件名
        current_date = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename_prefix = self.config['output']['html_filename_prefix']
        html_filename = f"{filename_prefix}_{current_date}.html"
        
        # 生成HTML内容
        html_content = self._create_html_content(anomaly_lists, summary_stats, actual_start_date, actual_end_date)
        
        # 写入文件
        try:
            with open(html_filename, 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            self.logger.info(f"HTML报告已生成: {html_filename}")
            return html_filename
            
        except Exception as e:
            self.logger.error(f"生成HTML报告失败: {e}")
            raise
    
    def _create_html_content(self, anomaly_lists: Dict[str, List[Dict[str, Any]]],
                           summary_stats: Dict[str, Any],
                           actual_start_date: str = None,
                           actual_end_date: str = None) -> str:
        """创建HTML内容"""
        
        html_template = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ES监控报告 - {{ report_date }}</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .header {
            background-color: #2c3e50;
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .summary {
            background-color: #ecf0f1;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .summary-item {
            display: inline-block;
            margin-right: 30px;
            font-weight: bold;
        }
        .controls {
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .control-group {
            margin-bottom: 15px;
        }
        .control-group label {
            display: inline-block;
            width: 100px;
            font-weight: bold;
        }
        .control-group input, .control-group select {
            padding: 5px;
            margin-right: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .control-group button {
            padding: 8px 15px;
            background-color: #3498db;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .control-group button:hover {
            background-color: #2980b9;
        }
        .section {
            background-color: white;
            margin-bottom: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .section-header {
            background-color: #3498db;
            color: white;
            padding: 15px;
            border-radius: 8px 8px 0 0;
            font-weight: bold;
            font-size: 16px;
        }
        .table-container {
            padding: 15px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            font-size: 14px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f8f9fa;
            font-weight: bold;
            cursor: pointer;
            position: relative;
        }
        th:hover {
            background-color: #e9ecef;
        }
        th.sortable::after {
            content: ' ↕';
            color: #999;
        }
        th.sort-asc::after {
            content: ' ↑';
            color: #333;
        }
        th.sort-desc::after {
            content: ' ↓';
            color: #333;
        }
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        .change-rate {
            font-weight: bold;
        }
        .change-rate.positive {
            color: #e74c3c;
        }
        .change-rate.negative {
            color: #27ae60;
        }
        .no-data {
            text-align: center;
            color: #7f8c8d;
            font-style: italic;
            padding: 20px;
        }
        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Elasticsearch 监控报告</h1>
        <p>生成时间: {{ report_date }}</p>
        <p>监控期间: {{ start_date }} 至 {{ end_date }}</p>
    </div>
    
    <div class="summary">
        <h2>摘要统计</h2>
        <div class="summary-item">分析字段总数: {{ summary_stats.total_fields_analyzed }}</div>
        <div class="summary-item">有异常的字段数: {{ summary_stats.fields_with_anomalies }}</div>
        <div class="summary-item">异常记录总数: {{ summary_stats.total_anomalies }}</div>
        <div class="summary-item">门限值: {{ summary_stats.threshold_used }}%</div>
    </div>

    <div class="controls">
        <h3>过滤和排序控制</h3>
        <div class="control-group">
            <label>过滤IP:</label>
            <input type="text" id="filterIP" placeholder="输入IP地址过滤">
            <button onclick="applyFilters()">应用过滤</button>
            <button onclick="clearFilters()">清除过滤</button>
        </div>
        <div class="control-group">
            <label>过滤版本:</label>
            <input type="text" id="filterVersion" placeholder="输入版本信息过滤">
        </div>
        <div class="control-group">
            <label>显示列:</label>
            <label><input type="checkbox" id="col-starttime" checked> 开始时间</label>
            <label><input type="checkbox" id="col-endtime" checked> 结束时间</label>
            <label><input type="checkbox" id="col-ip" checked> IP</label>
            <label><input type="checkbox" id="col-version" checked> 版本信息</label>
            <label><input type="checkbox" id="col-startvalue" checked> 开始数值</label>
            <label><input type="checkbox" id="col-endvalue" checked> 结束数值</label>
            <label><input type="checkbox" id="col-changerate" checked> 变化率</label>
            <button onclick="applyColumnVisibility()">应用列设置</button>
        </div>
    </div>
    
    {% if anomaly_lists %}
        {% for list_name, anomalies in anomaly_lists.items() %}
        <div class="section">
            <div class="section-header">
                {{ list_name }} ({{ anomalies|length }} 个异常)
            </div>
            <div class="table-container">
                <table>
                    <thead>
                        <tr>
                            <th class="sortable col-starttime" onclick="sortTable(this, 0)">开始时间</th>
                            <th class="sortable col-endtime" onclick="sortTable(this, 1)">结束时间</th>
                            <th class="sortable col-ip" onclick="sortTable(this, 2)">IP</th>
                            <th class="sortable col-version" onclick="sortTable(this, 3)">版本信息</th>
                            <th class="sortable col-startvalue" onclick="sortTable(this, 4)">开始数值</th>
                            <th class="sortable col-endvalue" onclick="sortTable(this, 5)">结束数值</th>
                            <th class="sortable col-changerate" onclick="sortTable(this, 6)">变化率(%)</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for anomaly in anomalies %}
                        <tr>
                            <td>{{ anomaly.StartTime }}</td>
                            <td>{{ anomaly.EndTime }}</td>
                            <td>{{ anomaly.IP }}</td>
                            <td>{{ anomaly.VersionInfo }}</td>
                            <td>{{ anomaly.StartValue }}</td>
                            <td>{{ anomaly.EndValue }}</td>
                            <td class="change-rate positive">
                                {{ anomaly.ChangeRate }}%
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
        {% endfor %}
    {% else %}
        <div class="section">
            <div class="section-header">监控结果</div>
            <div class="no-data">
                在指定的监控期间内，所有字段的变化都在正常范围内，未检测到超过门限值的异常。
            </div>
        </div>
    {% endif %}
    
    <div style="margin-top: 30px; text-align: center; color: #7f8c8d; font-size: 12px;">
        <p>报告由 ES Monitor 系统自动生成</p>
    </div>
</body>
</html>
        """
        
        template = Template(html_template)
        
        # 使用实际日期或配置中的日期
        display_start_date = actual_start_date or self.config['monitoring']['start_date']
        display_end_date = actual_end_date or self.config['monitoring']['end_date']

        return template.render(
            report_date=datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            start_date=display_start_date,
            end_date=display_end_date,
            anomaly_lists=anomaly_lists,
            summary_stats=summary_stats
        )
