"""
异常数值监控分析器
负责检测超过指定门限值的异常数据
"""

import logging
from typing import Dict, List, Any, Optional
from datetime import datetime
from elasticsearch import Elasticsearch


class ThresholdAnalyzer:
    """异常数值监控分析器"""
    
    def __init__(self, es_client: Elasticsearch, config: Dict[str, Any]):
        self.es_client = es_client
        self.config = config
        self.logger = logging.getLogger(__name__)
        self.anomaly_lists = {}

        # 从配置文件读取门限值配置
        self.threshold_config = config.get('threshold_monitoring', {})
    
    def analyze_all_data(self) -> Dict[str, List[Dict[str, Any]]]:
        """分析所有索引的异常数值"""
        self.logger.info("开始异常数值监控分析")

        for index_name in self.config['indices'].keys():
            if index_name in self.threshold_config:
                self.logger.info(f"开始分析索引 {index_name} 的异常数值")
                self._analyze_index(index_name)

        return self.anomaly_lists
    
    def _analyze_index(self, index_name: str):
        """分析单个索引的异常数值"""
        threshold_fields = self.threshold_config[index_name]
        
        for field_name, threshold_value in threshold_fields.items():
            self.logger.info(f"开始分析索引 {index_name} 字段 {field_name} 的异常数值")
            
            # 获取所有IP列表
            ip_list = self._get_all_ips_for_index(index_name)
            self.logger.info(f"索引 {index_name} 包含IP: {', '.join(ip_list)}")
            
            # 合并所有IP的异常数据到一个列表中
            list_key = f"{index_name}-{field_name}"
            all_anomalies = []
            
            # 遍历每个IP分别进行分析
            for ip in ip_list:
                anomalies = self._analyze_field_threshold(index_name, field_name, ip, threshold_value)
                
                if anomalies:
                    all_anomalies.extend(anomalies)
                    self.logger.info(f"索引 {index_name} 字段 {field_name} IP {ip} 检测到 {len(anomalies)} 个异常数值")
                else:
                    self.logger.info(f"索引 {index_name} 字段 {field_name} IP {ip} 未检测到异常数值")
            
            # 如果有异常数据，添加到列表中
            if all_anomalies:
                self.anomaly_lists[list_key] = all_anomalies
                self.logger.info(f"字段 {list_key} 总共检测到 {len(all_anomalies)} 个异常数值")
    
    def _get_all_ips_for_index(self, index_name: str) -> List[str]:
        """获取指定索引下的所有IP列表"""
        try:
            query = {
                "size": 0,
                "aggs": {
                    "unique_ips": {
                        "terms": {
                            "field": "IP.keyword",
                            "size": 1000
                        }
                    }
                }
            }
            
            response = self.es_client.search(index=index_name, body=query)
            ip_buckets = response['aggregations']['unique_ips']['buckets']
            return [bucket['key'] for bucket in ip_buckets]
            
        except Exception as e:
            self.logger.error(f"获取索引 {index_name} 的IP列表失败: {e}")
            return []
    
    def _analyze_field_threshold(self, index_name: str, field_name: str, ip: str, threshold_value: float) -> List[Dict[str, Any]]:
        """分析指定字段的异常数值"""
        try:
            # 首先尝试获取索引的映射信息来找到时间戳字段
            timestamp_field = self._find_timestamp_field(index_name)

            # 构建查询的must条件
            must_conditions = [
                {"term": {"IP.keyword": ip}},
                {"range": {field_name: {"gt": threshold_value}}}
            ]

            # 如果找到时间戳字段，添加时间范围过滤
            if timestamp_field:
                must_conditions.append({
                    "range": {timestamp_field: {
                        "gte": self.config['monitoring']['start_date'],
                        "lte": self.config['monitoring']['end_date']
                    }}
                })

            # 构建查询
            query = {
                "query": {
                    "bool": {
                        "must": must_conditions
                    }
                },
                "size": 10000
            }

            # 如果有时间戳字段，添加排序
            if timestamp_field:
                query["sort"] = [{timestamp_field: {"order": "asc"}}]
            
            response = self.es_client.search(index=index_name, body=query)
            hits = response['hits']['hits']
            
            anomalies = []
            for hit in hits:
                source = hit['_source']
                
                # 获取版本信息
                version_info = self._get_version_info(source)
                
                anomaly = {
                    'Index': index_name,
                    'Field': field_name,
                    'Time': source.get('@timestamp', ''),
                    'IP': source.get('IP', ''),
                    'Owner': source.get('ne_owner', ''),  # 获取ne_owner字段
                    'VersionInfo': version_info,
                    'Value': source.get(field_name, 0),
                    'Threshold': threshold_value
                }
                anomalies.append(anomaly)
            
            return anomalies
            
        except Exception as e:
            self.logger.error(f"分析字段 {index_name}.{field_name} IP {ip} 异常数值失败: {e}")
            return []
    
    def _get_version_info(self, record: Dict[str, Any]) -> str:
        """获取版本信息"""
        version_fields = ['ne_version', 'version', 'sw_version']
        for field in version_fields:
            if field in record and record[field]:
                return str(record[field])
        return 'unknown'
