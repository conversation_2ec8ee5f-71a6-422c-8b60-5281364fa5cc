# ES监控系统使用示例

## 快速开始

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 配置系统
编辑 `config.json` 文件：

```json
{
  "elasticsearch": {
    "host": "http://***************:9200"
  },
  "indices": {
    "psis-collector-cpu-index": {
      "fields": [
        "cpu_usage",
        "mem_usage", 
        "cpu_kmalloc_512_active_objs",
        "cpu_kmalloc_512_num_objs",
        "cpu_kmalloc_256_active_objs",
        "cpu_kmalloc_256_num_objs",
        "cpu_kmalloc_128_active_objs",
        "cpu_kmalloc_128_num_objs"
      ]
    },
    "psis-collector-harddisk-index": {
      "fields": [
        "mmcblk0p1_Avail",
        "mmcblk0p2_Avail",
        "mmcblk0p3_Avail",
        "mmcblk0p4_Avail",
        "tmpfs_sysfscgroupUsed",
        "devtmpfs_devUsage",
        "tmpfs_runUsage",
        "tmpfs_tmpUsage",
        "tmpfs_sysfscgroupUsage",
        "tmpfs_runlockUsage",
        "rootUsage"
      ]
    }
  },
  "monitoring": {
    "threshold_value": 5.0,
    "start_date": "auto",
    "end_date": "auto"
  },
  "output": {
    "html_filename_prefix": "es_monitor_report"
  }
}
```

### 3. 运行测试
```bash
python test_system.py
```

### 4. 执行监控
```bash
python main.py
```

## 配置说明

### ES连接配置
- `host`: Elasticsearch服务器地址

### 索引和字段配置
- `indices`: 要监控的索引名称和字段列表
- 支持多个索引，每个索引可以配置不同的字段

### 监控参数
- `threshold_value`: 变化率门限值（百分比），超过此值将被标记为异常
- `start_date`: 监控开始日期 (YYYY-MM-DD)，设置为"auto"时自动使用前一周
- `end_date`: 监控结束日期 (YYYY-MM-DD)，设置为"auto"时自动使用当前日期

### 输出配置
- `html_filename_prefix`: HTML报告文件名前缀

## 系统特性

### 1. 智能数据过滤
- 自动识别时间戳字段（支持多种格式）
- 每天只获取第一条记录，避免重复数据干扰
- 按日期范围过滤数据
- **默认扫描前一周（7天）数据**

### 2. 按IP分组分析
- **按IP -> 索引 -> 字段的顺序进行扫描**
- 确保同一IP的数据进行比较
- 避免不同设备间的数据混淆

### 3. 灵活的数值处理
- 自动处理带单位的存储值（G、M、K、B）
- 支持百分比、逗号分隔的数值
- 智能类型转换

### 4. 趋势分析
- 计算相邻两天的变化率
- **只记录增长的变化率，忽略减少的变化**
- 检测超过门限值的异常变化

### 5. 详细报告
- 生成美观的HTML报告
- 按IP-索引-字段分组显示异常数据
- **新的表格格式**：开始时间、结束时间、IP、开始版本、结束版本、开始数值、结束数值、变化率
- 包含摘要统计和详细表格

## 运行结果示例

### 控制台输出
```
2025-06-10 15:43:42,085 - main - INFO - === ES监控系统启动 ===
2025-06-10 15:43:42,085 - main - INFO - 初始化ES监控器...
2025-06-10 15:43:42,085 - main - INFO - 测试ES连接...
2025-06-10 15:43:42,097 - es_monitor - INFO - ES连接成功，版本: 8.16.1
2025-06-10 15:43:42,098 - main - INFO - 监控日期范围: 2025-06-04 到 2025-06-10
2025-06-10 15:43:42,098 - main - INFO - 门限值: 5.0%
2025-06-10 15:43:42,490 - data_analyzer - INFO - 开始分析IP *************** 的数据
2025-06-10 15:43:42,518 - data_analyzer - INFO - 开始分析IP *************** 的数据
2025-06-10 15:43:42,518 - data_analyzer - INFO - 开始分析IP *************** 的数据
2025-06-10 15:43:42,531 - main - INFO - 分析完成 - 总字段数: 4, 异常字段数: 4, 异常记录数: 4
2025-06-10 15:43:42,544 - report_generator - INFO - HTML报告已生成: es_monitor_report_20250610_154342.html

✅ 监控报告已生成: es_monitor_report_20250610_154342.html
```

### HTML报告内容
- **摘要统计**: 分析字段总数、异常字段数、异常记录数
- **详细表格**: 按IP-索引-字段分组的异常变化详情
- **新表格格式**: 开始时间、结束时间、IP、开始版本、结束版本、开始数值、结束数值、变化率
- **只显示增长**: 所有变化率都是正数（增长的）

## 故障排除

### 1. ES连接问题
- 检查ES服务器地址是否正确
- 确认网络连接正常
- 验证ES服务是否运行

### 2. 数据获取问题
- 确认索引名称正确
- 检查日期范围是否有数据
- 验证字段名称是否存在

### 3. 字段值转换问题
- 检查字段值格式
- 确认数值类型正确
- 查看日志中的转换警告

## 扩展功能

### 添加新的索引
在 `config.json` 中添加新的索引配置：

```json
"new-index-name": {
  "fields": ["field1", "field2", "field3"]
}
```

### 调整门限值
根据实际需求调整 `threshold_value`：
- 较小值（如1%）：更敏感，检测更多变化
- 较大值（如10%）：较宽松，只检测显著变化

### 自定义日期范围
根据需要调整监控时间范围：
- 短期监控：最近几天或几周
- 长期监控：几个月或一年

## 最佳实践

1. **定期运行**: 建议每天或每周运行一次监控
2. **合理设置门限**: 根据业务特点调整门限值
3. **关注趋势**: 重点关注持续异常的字段
4. **及时响应**: 对异常变化及时分析和处理
