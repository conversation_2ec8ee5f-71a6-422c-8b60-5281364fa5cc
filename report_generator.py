"""
报告生成模块
负责生成HTML格式的监控报告
"""

import os
import logging
from datetime import datetime
from typing import Dict, List, Any
from jinja2 import Template


class ReportGenerator:
    """报告生成器"""
    
    def __init__(self, config: Dict[str, Any]):
        """初始化报告生成器"""
        self.config = config
        self.logger = self._setup_logger()
    
    def _setup_logger(self) -> logging.Logger:
        """设置日志"""
        logger = logging.getLogger('report_generator')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def generate_html_report(self, anomaly_lists: Dict[str, List[Dict[str, Any]]], 
                           summary_stats: Dict[str, Any]) -> str:
        """
        生成HTML报告
        
        Args:
            anomaly_lists: 异常数据列表
            summary_stats: 摘要统计
            
        Returns:
            生成的HTML文件路径
        """
        # 生成文件名
        current_date = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename_prefix = self.config['output']['html_filename_prefix']
        html_filename = f"{filename_prefix}_{current_date}.html"
        
        # 生成HTML内容
        html_content = self._create_html_content(anomaly_lists, summary_stats)
        
        # 写入文件
        try:
            with open(html_filename, 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            self.logger.info(f"HTML报告已生成: {html_filename}")
            return html_filename
            
        except Exception as e:
            self.logger.error(f"生成HTML报告失败: {e}")
            raise
    
    def _create_html_content(self, anomaly_lists: Dict[str, List[Dict[str, Any]]], 
                           summary_stats: Dict[str, Any]) -> str:
        """创建HTML内容"""
        
        html_template = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ES监控报告 - {{ report_date }}</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .header {
            background-color: #2c3e50;
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .summary {
            background-color: #ecf0f1;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .summary-item {
            display: inline-block;
            margin-right: 30px;
            font-weight: bold;
        }
        .section {
            background-color: white;
            margin-bottom: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .section-header {
            background-color: #3498db;
            color: white;
            padding: 15px;
            border-radius: 8px 8px 0 0;
            font-weight: bold;
            font-size: 16px;
        }
        .table-container {
            padding: 15px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            font-size: 14px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        .change-rate {
            font-weight: bold;
        }
        .change-rate.positive {
            color: #e74c3c;
        }
        .change-rate.negative {
            color: #27ae60;
        }
        .no-data {
            text-align: center;
            color: #7f8c8d;
            font-style: italic;
            padding: 20px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Elasticsearch 监控报告</h1>
        <p>生成时间: {{ report_date }}</p>
        <p>监控期间: {{ start_date }} 至 {{ end_date }}</p>
    </div>
    
    <div class="summary">
        <h2>摘要统计</h2>
        <div class="summary-item">分析字段总数: {{ summary_stats.total_fields_analyzed }}</div>
        <div class="summary-item">有异常的字段数: {{ summary_stats.fields_with_anomalies }}</div>
        <div class="summary-item">异常记录总数: {{ summary_stats.total_anomalies }}</div>
        <div class="summary-item">门限值: {{ summary_stats.threshold_used }}%</div>
    </div>
    
    {% if anomaly_lists %}
        {% for list_name, anomalies in anomaly_lists.items() %}
        <div class="section">
            <div class="section-header">
                {{ list_name }} ({{ anomalies|length }} 个异常)
            </div>
            <div class="table-container">
                <table>
                    <thead>
                        <tr>
                            <th>开始时间</th>
                            <th>结束时间</th>
                            <th>IP</th>
                            <th>开始版本</th>
                            <th>结束版本</th>
                            <th>开始数值</th>
                            <th>结束数值</th>
                            <th>变化率(%)</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for anomaly in anomalies %}
                        <tr>
                            <td>{{ anomaly.StartTime }}</td>
                            <td>{{ anomaly.EndTime }}</td>
                            <td>{{ anomaly.IP }}</td>
                            <td>{{ anomaly.StartVersion }}</td>
                            <td>{{ anomaly.EndVersion }}</td>
                            <td>{{ anomaly.StartValue }}</td>
                            <td>{{ anomaly.EndValue }}</td>
                            <td class="change-rate positive">
                                {{ anomaly.ChangeRate }}%
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
        {% endfor %}
    {% else %}
        <div class="section">
            <div class="section-header">监控结果</div>
            <div class="no-data">
                在指定的监控期间内，所有字段的变化都在正常范围内，未检测到超过门限值的异常。
            </div>
        </div>
    {% endif %}
    
    <div style="margin-top: 30px; text-align: center; color: #7f8c8d; font-size: 12px;">
        <p>报告由 ES Monitor 系统自动生成</p>
    </div>
</body>
</html>
        """
        
        template = Template(html_template)
        
        return template.render(
            report_date=datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            start_date=self.config['monitoring']['start_date'],
            end_date=self.config['monitoring']['end_date'],
            anomaly_lists=anomaly_lists,
            summary_stats=summary_stats
        )
