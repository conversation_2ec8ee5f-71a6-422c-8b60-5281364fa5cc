"""
ES监控系统主入口
"""

import sys
import logging
from typing import Optional
from es_monitor import ESMonitor
from data_analyzer import DataAnalyzer
from report_generator import ReportGenerator


def setup_main_logger() -> logging.Logger:
    """设置主日志器"""
    logger = logging.getLogger('main')
    logger.setLevel(logging.INFO)
    
    if not logger.handlers:
        handler = logging.StreamHandler()
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        handler.setFormatter(formatter)
        logger.addHandler(handler)
    
    return logger


def main(config_path: str = "config.json") -> Optional[str]:
    """
    主函数
    
    Args:
        config_path: 配置文件路径
        
    Returns:
        生成的HTML报告文件路径，如果失败则返回None
    """
    logger = setup_main_logger()
    
    try:
        logger.info("=== ES监控系统启动 ===")
        
        # 1. 初始化ES监控器
        logger.info("初始化ES监控器...")
        es_monitor = ESMonitor(config_path)
        
        # 2. 测试ES连接
        logger.info("测试ES连接...")
        if not es_monitor.test_connection():
            logger.error("ES连接失败，程序退出")
            return None
        
        # 3. 获取配置参数
        config = es_monitor.config
        start_date, end_date = es_monitor.get_date_range(
            config['monitoring']['start_date'],
            config['monitoring']['end_date']
        )
        
        logger.info(f"监控日期范围: {start_date} 到 {end_date}")
        logger.info(f"门限值: {config['monitoring']['threshold_value']}%")
        
        # 4. 获取所有索引数据
        logger.info("获取索引数据...")
        all_data = es_monitor.get_all_indices_data(start_date, end_date)
        
        if not all_data:
            logger.warning("未获取到任何数据")
            return None
        
        # 打印数据获取情况
        for index_name, data in all_data.items():
            logger.info(f"索引 {index_name}: 获取到 {len(data)} 天的数据")
        
        # 5. 初始化数据分析器
        logger.info("初始化数据分析器...")
        analyzer = DataAnalyzer(config)
        
        # 6. 分析数据趋势
        logger.info("开始分析数据趋势...")
        anomaly_lists = analyzer.analyze_all_data(all_data)
        
        # 7. 获取摘要统计
        summary_stats = analyzer.get_summary_stats()
        logger.info(f"分析完成 - 总字段数: {summary_stats['total_fields_analyzed']}, "
                   f"异常字段数: {summary_stats['fields_with_anomalies']}, "
                   f"异常记录数: {summary_stats['total_anomalies']}")
        
        # 8. 生成HTML报告
        logger.info("生成HTML报告...")
        report_generator = ReportGenerator(config)
        html_file = report_generator.generate_html_report(anomaly_lists, summary_stats, start_date, end_date)
        
        logger.info(f"=== 监控完成，报告已生成: {html_file} ===")
        return html_file
        
    except Exception as e:
        logger.error(f"程序执行出错: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return None


if __name__ == "__main__":
    # 支持命令行参数指定配置文件
    config_file = sys.argv[1] if len(sys.argv) > 1 else "config.json"
    
    result = main(config_file)
    
    if result:
        print(f"\n✅ 监控报告已生成: {result}")
        print("请打开HTML文件查看详细结果。")
    else:
        print("\n❌ 监控执行失败，请检查日志信息。")
        sys.exit(1)
