"""
Elasticsearch监控模块
负责连接ES并获取数据
"""

import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from elasticsearch import Elasticsearch
from dateutil.parser import parse as parse_date


class ESMonitor:
    """Elasticsearch监控类"""
    
    def __init__(self, config_path: str = "config.json"):
        """初始化ES监控器"""
        self.config = self._load_config(config_path)
        self.es_client = self._create_es_client()
        self.logger = self._setup_logger()
    
    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            raise FileNotFoundError(f"配置文件 {config_path} 不存在")
        except json.JSONDecodeError as e:
            raise ValueError(f"配置文件格式错误: {e}")
    
    def _create_es_client(self) -> Elasticsearch:
        """创建ES客户端"""
        es_config = self.config['elasticsearch']
        return Elasticsearch([es_config['host']])
    
    def _setup_logger(self) -> logging.Logger:
        """设置日志"""
        logger = logging.getLogger('es_monitor')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger

    def _find_timestamp_field(self, index_name: str) -> str:
        """查找索引中的时间戳字段"""
        try:
            # 常见的时间戳字段名称
            timestamp_candidates = ['@timestamp', 'timestamp', 'time', 'date', 'created_at', 'updated_at']

            # 获取索引映射
            mapping = self.es_client.indices.get_mapping(index=index_name)

            if index_name in mapping:
                properties = mapping[index_name]['mappings'].get('properties', {})

                # 查找时间戳字段
                for field_name in timestamp_candidates:
                    if field_name in properties:
                        field_type = properties[field_name].get('type', '')
                        if field_type in ['date', 'date_nanos']:
                            self.logger.info(f"找到时间戳字段: {field_name}")
                            return field_name

                # 如果没有找到标准字段，查找所有date类型字段
                for field_name, field_info in properties.items():
                    if field_info.get('type') in ['date', 'date_nanos']:
                        self.logger.info(f"找到日期类型字段: {field_name}")
                        return field_name

            self.logger.warning(f"索引 {index_name} 中未找到时间戳字段")
            return ""

        except Exception as e:
            self.logger.warning(f"获取索引 {index_name} 映射信息失败: {e}")
            return ""

    def get_daily_first_records(self, index_name: str, start_date: str, end_date: str) -> List[Dict[str, Any]]:
        """
        获取指定日期范围内每天的第一条记录

        Args:
            index_name: 索引名称
            start_date: 开始日期 (YYYY-MM-DD)
            end_date: 结束日期 (YYYY-MM-DD)

        Returns:
            每天第一条记录的列表
        """
        try:
            start_dt = parse_date(start_date)
            end_dt = parse_date(end_date)

            # 首先尝试获取索引的映射信息来找到时间戳字段
            timestamp_field = self._find_timestamp_field(index_name)

            # 构建查询
            if timestamp_field:
                query = {
                    "query": {
                        "range": {
                            timestamp_field: {
                                "gte": start_dt.isoformat(),
                                "lte": end_dt.isoformat()
                            }
                        }
                    },
                    "sort": [
                        {timestamp_field: {"order": "asc"}}
                    ],
                    "size": 10000  # 根据实际需要调整
                }
            else:
                # 如果没有找到时间戳字段，则获取所有数据
                query = {
                    "query": {"match_all": {}},
                    "size": 10000
                }
            
            self.logger.info(f"查询索引 {index_name}，日期范围: {start_date} 到 {end_date}")
            
            # 执行查询
            response = self.es_client.search(index=index_name, body=query)
            hits = response['hits']['hits']
            
            if not hits:
                self.logger.warning(f"索引 {index_name} 在指定日期范围内没有数据")
                return []
            
            # 按日期分组，每天只取第一条
            daily_records = {}
            for hit in hits:
                source = hit['_source']

                # 尝试从多个可能的时间戳字段获取时间
                timestamp = None
                for ts_field in [timestamp_field, '@timestamp', 'timestamp', 'time', 'date']:
                    if ts_field and ts_field in source:
                        timestamp = source[ts_field]
                        break

                if timestamp:
                    # 提取日期部分
                    if 'T' in str(timestamp):
                        date_str = str(timestamp).split('T')[0]
                    else:
                        # 如果时间戳格式不标准，尝试其他方式
                        date_str = str(timestamp)[:10]

                    # 如果这一天还没有记录，则添加
                    if date_str not in daily_records:
                        daily_records[date_str] = source
                else:
                    # 如果没有时间戳，使用当前日期
                    from datetime import datetime
                    date_str = datetime.now().strftime('%Y-%m-%d')
                    if date_str not in daily_records:
                        daily_records[date_str] = source

            # 按日期排序返回
            sorted_records = []
            for date_str in sorted(daily_records.keys()):
                record = daily_records[date_str]
                record['TryDate'] = date_str

                # 提取时间部分
                timestamp_value = None
                for ts_field in [timestamp_field, '@timestamp', 'timestamp', 'time', 'date']:
                    if ts_field and ts_field in record:
                        timestamp_value = record[ts_field]
                        break

                if timestamp_value and 'T' in str(timestamp_value):
                    record['TryTime'] = str(timestamp_value).split('T')[1]
                else:
                    record['TryTime'] = ''

                sorted_records.append(record)
            
            self.logger.info(f"从索引 {index_name} 获取到 {len(sorted_records)} 天的数据")
            return sorted_records
            
        except Exception as e:
            self.logger.error(f"获取索引 {index_name} 数据时出错: {e}")
            return []
    
    def get_all_indices_data(self, start_date: str, end_date: str) -> Dict[str, List[Dict[str, Any]]]:
        """
        获取所有配置索引的数据
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            索引名称到数据列表的映射
        """
        all_data = {}
        
        for index_name in self.config['indices'].keys():
            data = self.get_daily_first_records(index_name, start_date, end_date)
            all_data[index_name] = data
        
        return all_data
    
    def test_connection(self) -> bool:
        """测试ES连接"""
        try:
            info = self.es_client.info()
            self.logger.info(f"ES连接成功，版本: {info['version']['number']}")
            return True
        except Exception as e:
            self.logger.error(f"ES连接失败: {e}")
            return False
