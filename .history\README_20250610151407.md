# ES监控系统

这是一个用于监控Elasticsearch索引字段变化趋势的Python系统。系统会扫描指定索引的字段数据，检测超过门限值的变化，并生成详细的HTML报告。

## 功能特性

- 🔍 **智能数据过滤**: 每天只获取第一条记录，避免重复数据干扰
- 📊 **趋势分析**: 计算字段的日变化率，检测异常波动
- 🚨 **门限监控**: 可配置的变化率门限值，自动识别异常
- 📋 **详细报告**: 生成包含异常数据的HTML表格报告
- ⚙️ **灵活配置**: JSON配置文件，支持多索引、多字段监控

## 系统架构

```
├── config.json          # 配置文件
├── main.py              # 主入口程序
├── es_monitor.py        # ES连接和数据获取
├── data_analyzer.py     # 数据分析和趋势检测
├── report_generator.py  # HTML报告生成
├── requirements.txt     # 依赖包列表
└── README.md           # 说明文档
```

## 安装依赖

```bash
pip install -r requirements.txt
```

## 配置说明

编辑 `config.json` 文件：

```json
{
  "elasticsearch": {
    "host": "http://***************:9200"
  },
  "indices": {
    "psis-collector-cpu-index": {
      "fields": ["cpu_usage", "mem_usage", ...]
    },
    "psis-collector-harddisk-index": {
      "fields": ["mmcblk0p1_Avail", "mmcblk0p2_Avail", ...]
    }
  },
  "monitoring": {
    "threshold_value": 5.0,
    "start_date": "2024-01-01",
    "end_date": "2024-12-31"
  }
}
```

### 配置参数说明

- **elasticsearch.host**: ES服务器地址
- **indices**: 要监控的索引和字段配置
- **monitoring.threshold_value**: 变化率门限值（百分比）
- **monitoring.start_date/end_date**: 监控日期范围

## 使用方法

### 基本使用

```bash
python main.py
```

### 指定配置文件

```bash
python main.py custom_config.json
```

## 监控逻辑

1. **数据获取**: 连接ES，获取指定日期范围内的数据
2. **数据过滤**: 每天只保留第一条记录
3. **趋势分析**: 计算相邻两天的字段变化率
4. **异常检测**: 识别超过门限值的变化
5. **报告生成**: 生成包含异常数据的HTML报告

## 输出报告

系统会生成以下格式的HTML报告：
- 文件名: `es_monitor_report_YYYYMMDD_HHMMSS.html`
- 包含摘要统计和详细的异常数据表格
- 支持按索引-字段分组显示

## 监控字段

### CPU索引 (psis-collector-cpu-index)
- cpu_usage
- mem_usage
- cpu_kmalloc_512_active_objs
- cpu_kmalloc_512_num_objs
- cpu_kmalloc_256_active_objs
- cpu_kmalloc_256_num_objs
- cpu_kmalloc_128_active_objs
- cpu_kmalloc_128_num_objs

### 硬盘索引 (psis-collector-harddisk-index)
- mmcblk0p1_Avail
- mmcblk0p2_Avail
- mmcblk0p3_Avail
- mmcblk0p4_Avail
- tmpfs_sysfscgroupUsed
- devtmpfs_devUsage
- tmpfs_runUsage
- tmpfs_tmpUsage
- tmpfs_sysfscgroupUsage
- tmpfs_runlockUsage
- rootUsage

## 故障排除

1. **ES连接失败**: 检查ES服务器地址和网络连接
2. **无数据返回**: 确认索引名称和日期范围正确
3. **字段值错误**: 检查字段名称是否存在于数据中

## 日志信息

系统会输出详细的日志信息，包括：
- ES连接状态
- 数据获取进度
- 异常检测结果
- 报告生成状态
