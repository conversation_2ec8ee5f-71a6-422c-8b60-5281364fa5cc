
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ES监控报告 - 2025-06-10 16:21:06</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .header {
            background-color: #2c3e50;
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .summary {
            background-color: #ecf0f1;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .summary-item {
            display: inline-block;
            margin-right: 30px;
            font-weight: bold;
        }
        .controls {
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .control-group {
            margin-bottom: 15px;
        }
        .control-group label {
            display: inline-block;
            width: 100px;
            font-weight: bold;
        }
        .control-group input, .control-group select {
            padding: 5px;
            margin-right: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .control-group button {
            padding: 8px 15px;
            background-color: #3498db;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .control-group button:hover {
            background-color: #2980b9;
        }
        .section {
            background-color: white;
            margin-bottom: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .section-header {
            background-color: #3498db;
            color: white;
            padding: 15px;
            border-radius: 8px 8px 0 0;
            font-weight: bold;
            font-size: 16px;
        }
        .table-container {
            padding: 15px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            font-size: 14px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f8f9fa;
            font-weight: bold;
            cursor: pointer;
            position: relative;
        }
        th:hover {
            background-color: #e9ecef;
        }
        th.sortable::after {
            content: ' ↕';
            color: #999;
        }
        th.sort-asc::after {
            content: ' ↑';
            color: #333;
        }
        th.sort-desc::after {
            content: ' ↓';
            color: #333;
        }
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        .change-rate {
            font-weight: bold;
        }
        .change-rate.positive {
            color: #e74c3c;
        }
        .change-rate.negative {
            color: #27ae60;
        }
        .no-data {
            text-align: center;
            color: #7f8c8d;
            font-style: italic;
            padding: 20px;
        }
        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Elasticsearch 监控报告</h1>
        <p>生成时间: 2025-06-10 16:21:06</p>
        <p>监控期间: 2024-01-01 至 2024-01-03</p>
    </div>
    
    <div class="summary">
        <h2>摘要统计</h2>
        <div class="summary-item">分析字段总数: 1</div>
        <div class="summary-item">有异常的字段数: 1</div>
        <div class="summary-item">异常记录总数: 1</div>
        <div class="summary-item">门限值: 5.0%</div>
    </div>

    <div class="controls">
        <h3>过滤和排序控制</h3>
        <div class="control-group">
            <label>过滤IP:</label>
            <input type="text" id="filterIP" placeholder="输入IP地址过滤">
            <button onclick="applyFilters()">应用过滤</button>
            <button onclick="clearFilters()">清除过滤</button>
        </div>
        <div class="control-group">
            <label>过滤版本:</label>
            <input type="text" id="filterVersion" placeholder="输入版本信息过滤">
        </div>
        <div class="control-group">
            <label>显示列:</label>
            <label><input type="checkbox" id="col-starttime" checked> 开始时间</label>
            <label><input type="checkbox" id="col-endtime" checked> 结束时间</label>
            <label><input type="checkbox" id="col-ip" checked> IP</label>
            <label><input type="checkbox" id="col-version" checked> 版本信息</label>
            <label><input type="checkbox" id="col-startvalue" checked> 开始数值</label>
            <label><input type="checkbox" id="col-endvalue" checked> 结束数值</label>
            <label><input type="checkbox" id="col-changerate" checked> 变化率</label>
            <button onclick="applyColumnVisibility()">应用列设置</button>
        </div>
    </div>
    
    
        
        <div class="section">
            <div class="section-header">
                ***********-test-index-cpu_usage (1 个异常)
            </div>
            <div class="table-container">
                <table>
                    <thead>
                        <tr>
                            <th class="sortable col-starttime" onclick="sortTable(this, 0)">开始时间</th>
                            <th class="sortable col-endtime" onclick="sortTable(this, 1)">结束时间</th>
                            <th class="sortable col-ip" onclick="sortTable(this, 2)">IP</th>
                            <th class="sortable col-version" onclick="sortTable(this, 3)">版本信息</th>
                            <th class="sortable col-startvalue" onclick="sortTable(this, 4)">开始数值</th>
                            <th class="sortable col-endvalue" onclick="sortTable(this, 5)">结束数值</th>
                            <th class="sortable col-changerate" onclick="sortTable(this, 6)">变化率(%)</th>
                        </tr>
                    </thead>
                    <tbody>
                        
                        <tr class="data-row">
                            <td class="col-starttime">2024-01-01 10:00:00</td>
                            <td class="col-endtime">2024-01-02 10:00:00</td>
                            <td class="col-ip">***********</td>
                            <td class="col-version"></td>
                            <td class="col-startvalue">50.0</td>
                            <td class="col-endvalue">60.0</td>
                            <td class="col-changerate change-rate positive">
                                20.0%
                            </td>
                        </tr>
                        
                    </tbody>
                </table>
            </div>
        </div>
        
    
    
    <div style="margin-top: 30px; text-align: center; color: #7f8c8d; font-size: 12px;">
        <p>报告由 ES Monitor 系统自动生成</p>
    </div>

    <script>
        // 排序功能
        function sortTable(header, columnIndex) {
            const table = header.closest('table');
            const tbody = table.querySelector('tbody');
            const rows = Array.from(tbody.querySelectorAll('tr.data-row'));

            // 确定排序方向
            let isAsc = header.classList.contains('sort-desc');

            // 清除所有排序标记
            table.querySelectorAll('th').forEach(th => {
                th.classList.remove('sort-asc', 'sort-desc');
            });

            // 设置当前列的排序标记
            header.classList.add(isAsc ? 'sort-asc' : 'sort-desc');

            // 排序行
            rows.sort((a, b) => {
                const aVal = a.cells[columnIndex].textContent.trim();
                const bVal = b.cells[columnIndex].textContent.trim();

                // 尝试数值比较
                const aNum = parseFloat(aVal.replace('%', ''));
                const bNum = parseFloat(bVal.replace('%', ''));

                if (!isNaN(aNum) && !isNaN(bNum)) {
                    return isAsc ? aNum - bNum : bNum - aNum;
                } else {
                    return isAsc ? aVal.localeCompare(bVal) : bVal.localeCompare(aVal);
                }
            });

            // 重新插入排序后的行
            rows.forEach(row => tbody.appendChild(row));
        }

        // 过滤功能
        function applyFilters() {
            const ipFilter = document.getElementById('filterIP').value.toLowerCase();
            const versionFilter = document.getElementById('filterVersion').value.toLowerCase();

            document.querySelectorAll('tr.data-row').forEach(row => {
                const ip = row.querySelector('.col-ip').textContent.toLowerCase();
                const version = row.querySelector('.col-version').textContent.toLowerCase();

                const ipMatch = !ipFilter || ip.includes(ipFilter);
                const versionMatch = !versionFilter || version.includes(versionFilter);

                if (ipMatch && versionMatch) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        }

        // 清除过滤
        function clearFilters() {
            document.getElementById('filterIP').value = '';
            document.getElementById('filterVersion').value = '';
            document.querySelectorAll('tr.data-row').forEach(row => {
                row.style.display = '';
            });
        }

        // 列显示控制
        function applyColumnVisibility() {
            const columns = ['starttime', 'endtime', 'ip', 'version', 'startvalue', 'endvalue', 'changerate'];

            columns.forEach((col, index) => {
                const checkbox = document.getElementById(`col-${col}`);
                const isVisible = checkbox.checked;

                // 控制表头
                document.querySelectorAll(`.col-${col}`).forEach(element => {
                    element.style.display = isVisible ? '' : 'none';
                });
            });
        }

        // 页面加载时的初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 为过滤输入框添加实时过滤
            document.getElementById('filterIP').addEventListener('input', applyFilters);
            document.getElementById('filterVersion').addEventListener('input', applyFilters);
        });
    </script>
</body>
</html>
        