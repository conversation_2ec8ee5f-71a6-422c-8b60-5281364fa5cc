"""
异常数值监控分析器
负责检测超过指定门限值的异常数据
"""

import logging
from typing import Dict, List, Any, Optional
from datetime import datetime
from elasticsearch import Elasticsearch


class ThresholdAnalyzer:
    """异常数值监控分析器"""
    
    def __init__(self, es_client: Elasticsearch, config: Dict[str, Any]):
        self.es_client = es_client
        self.config = config
        self.logger = logging.getLogger(__name__)
        self.anomaly_lists = {}

        # 从配置文件读取门限值配置
        self.threshold_config = config.get('threshold_monitoring', {})

        # 存储检查结果的字典，key格式为 check_{index_name}_{field_name}
        self.check_results = {}
    
    def analyze_all_data(self) -> Dict[str, List[Dict[str, Any]]]:
        """分析所有索引的异常数值"""
        self.logger.info("开始异常数值监控分析")

        for index_name in self.config['indices'].keys():
            if index_name in self.threshold_config:
                self.logger.info(f"开始分析索引 {index_name} 的异常数值")
                self._analyze_index(index_name)

        return self.anomaly_lists

    def get_check_results(self) -> Dict[str, List[Dict[str, Any]]]:
        """获取检查结果，返回格式为 check_{index_name}_{field_name} 的字典"""
        return self.check_results
    
    def _analyze_index(self, index_name: str):
        """分析单个索引的异常数值"""
        threshold_fields = self.threshold_config[index_name]

        for field_name, threshold_value in threshold_fields.items():
            self.logger.info(f"开始分析索引 {index_name} 字段 {field_name} 的异常数值")

            # 步骤1: 获取每天每个IP每个字段的最大值
            daily_max_data = self._get_daily_max_values(index_name, field_name)

            # 步骤2: 根据日期范围过滤
            filtered_data = self._filter_by_date_range(daily_max_data)

            # 步骤3: 比较门限值并生成结果
            check_list_key = f"check_{index_name}_{field_name}"
            self.check_results[check_list_key] = []

            for record in filtered_data:
                if record['value'] > threshold_value:
                    anomaly_record = {
                        'TryDate': record['date'],
                        'IP': record['ip'],
                        'Owner': record.get('owner', ''),
                        'Version': record.get('version', ''),
                        '字段名': field_name,
                        '数值': record['value']
                    }
                    self.check_results[check_list_key].append(anomaly_record)

            # 为了兼容现有的报告生成，也保留旧格式
            if self.check_results[check_list_key]:
                list_key = f"{index_name}-{field_name}"
                self.anomaly_lists[list_key] = self.check_results[check_list_key]
                self.logger.info(f"字段 {check_list_key} 检测到 {len(self.check_results[check_list_key])} 个异常")
    
    def _get_daily_max_values(self, index_name: str, field_name: str) -> List[Dict[str, Any]]:
        """获取每天每个IP每个字段的最大值"""
        try:
            # 首先尝试获取索引的映射信息来找到时间戳字段
            timestamp_field = self._find_timestamp_field(index_name)
            if not timestamp_field:
                self.logger.warning(f"索引 {index_name} 未找到时间戳字段，使用无时间过滤查询")
                return self._get_max_values_without_timestamp(index_name, field_name)

            # 构建聚合查询：按日期和IP分组，获取字段最大值
            query = {
                "size": 0,
                "aggs": {
                    "daily_data": {
                        "date_histogram": {
                            "field": timestamp_field,
                            "calendar_interval": "1d",
                            "format": "yyyy-MM-dd"
                        },
                        "aggs": {
                            "ip_data": {
                                "terms": {
                                    "field": "IP.keyword",
                                    "size": 1000
                                },
                                "aggs": {
                                    "max_value": {
                                        "max": {
                                            "field": field_name
                                        }
                                    },
                                    "sample_doc": {
                                        "top_hits": {
                                            "size": 1,
                                            "_source": ["ne_owner", "ne_version", "version", "sw_version"]
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }

            response = self.es_client.search(index=index_name, body=query)
            daily_buckets = response['aggregations']['daily_data']['buckets']

            daily_max_data = []
            for daily_bucket in daily_buckets:
                date = daily_bucket['key_as_string']
                ip_buckets = daily_bucket['ip_data']['buckets']

                for ip_bucket in ip_buckets:
                    ip = ip_bucket['key']
                    max_value = ip_bucket['max_value']['value']

                    # 获取样本文档以获取owner和version信息
                    sample_hits = ip_bucket['sample_doc']['hits']['hits']
                    owner = ''
                    version = ''
                    if sample_hits:
                        source = sample_hits[0]['_source']
                        owner = source.get('ne_owner', '')
                        version = self._get_version_info(source)

                    if max_value is not None:
                        daily_max_data.append({
                            'date': date,
                            'ip': ip,
                            'value': max_value,
                            'owner': owner,
                            'version': version
                        })

            return daily_max_data

        except Exception as e:
            self.logger.error(f"获取索引 {index_name} 字段 {field_name} 每日最大值失败: {e}")
            return []

    def _get_max_values_without_timestamp(self, index_name: str, field_name: str) -> List[Dict[str, Any]]:
        """当没有时间戳字段时，获取每个IP的最大值"""
        try:
            query = {
                "size": 0,
                "aggs": {
                    "ip_data": {
                        "terms": {
                            "field": "IP.keyword",
                            "size": 1000
                        },
                        "aggs": {
                            "max_value": {
                                "max": {
                                    "field": field_name
                                }
                            },
                            "sample_doc": {
                                "top_hits": {
                                    "size": 1,
                                    "_source": ["ne_owner", "ne_version", "version", "sw_version"]
                                }
                            }
                        }
                    }
                }
            }

            response = self.es_client.search(index=index_name, body=query)
            ip_buckets = response['aggregations']['ip_data']['buckets']

            max_data = []
            for ip_bucket in ip_buckets:
                ip = ip_bucket['key']
                max_value = ip_bucket['max_value']['value']

                # 获取样本文档以获取owner和version信息
                sample_hits = ip_bucket['sample_doc']['hits']['hits']
                owner = ''
                version = ''
                if sample_hits:
                    source = sample_hits[0]['_source']
                    owner = source.get('ne_owner', '')
                    version = self._get_version_info(source)

                if max_value is not None:
                    max_data.append({
                        'date': 'unknown',  # 没有时间戳时使用unknown
                        'ip': ip,
                        'value': max_value,
                        'owner': owner,
                        'version': version
                    })

            return max_data

        except Exception as e:
            self.logger.error(f"获取索引 {index_name} 字段 {field_name} 最大值失败: {e}")
            return []

    def _filter_by_date_range(self, daily_max_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """根据配置的日期范围过滤数据"""
        start_date = self.config['monitoring']['start_date']
        end_date = self.config['monitoring']['end_date']

        filtered_data = []
        for record in daily_max_data:
            record_date = record['date']

            # 如果没有日期信息，保留记录
            if record_date == 'unknown':
                filtered_data.append(record)
                continue

            # 检查日期是否在范围内
            if start_date <= record_date <= end_date:
                filtered_data.append(record)

        return filtered_data

    def _analyze_field_threshold(self, index_name: str, field_name: str, ip: str, threshold_value: float) -> List[Dict[str, Any]]:
        """分析指定字段的异常数值"""
        try:
            # 首先尝试获取索引的映射信息来找到时间戳字段
            timestamp_field = self._find_timestamp_field(index_name)

            # 构建查询的must条件
            must_conditions = [
                {"term": {"IP.keyword": ip}},
                {"range": {field_name: {"gt": threshold_value}}}
            ]

            # 如果找到时间戳字段，添加时间范围过滤
            if timestamp_field:
                must_conditions.append({
                    "range": {timestamp_field: {
                        "gte": self.config['monitoring']['start_date'],
                        "lte": self.config['monitoring']['end_date']
                    }}
                })

            # 构建查询
            query = {
                "query": {
                    "bool": {
                        "must": must_conditions
                    }
                },
                "size": 10000
            }

            # 如果有时间戳字段，添加排序
            if timestamp_field:
                query["sort"] = [{timestamp_field: {"order": "asc"}}]
            
            response = self.es_client.search(index=index_name, body=query)
            hits = response['hits']['hits']
            
            anomalies = []
            for hit in hits:
                source = hit['_source']

                # 获取时间戳信息
                timestamp = None
                for ts_field in [timestamp_field, '@timestamp', 'timestamp', 'time', 'date']:
                    if ts_field and ts_field in source:
                        timestamp = source[ts_field]
                        break

                # 获取版本信息
                version_info = self._get_version_info(source)

                anomaly = {
                    'Index': index_name,
                    'Field': field_name,
                    'Time': timestamp or '',
                    'IP': source.get('IP', ''),
                    'Owner': source.get('ne_owner', ''),  # 获取ne_owner字段
                    'VersionInfo': version_info,
                    'Value': source.get(field_name, 0),
                    'Threshold': threshold_value
                }
                anomalies.append(anomaly)
            
            return anomalies
            
        except Exception as e:
            self.logger.error(f"分析字段 {index_name}.{field_name} IP {ip} 异常数值失败: {e}")
            return []
    
    def _find_timestamp_field(self, index_name: str) -> Optional[str]:
        """查找索引中的时间戳字段"""
        try:
            # 获取索引映射
            mapping = self.es_client.indices.get_mapping(index=index_name)
            properties = mapping[index_name]['mappings']['properties']

            # 常见的时间戳字段名
            timestamp_candidates = ['@timestamp', 'timestamp', 'time', 'date', 'datetime']

            for field_name in timestamp_candidates:
                if field_name in properties:
                    field_type = properties[field_name].get('type', '')
                    if field_type == 'date':
                        self.logger.info(f"索引 {index_name} 使用时间戳字段: {field_name}")
                        return field_name

            self.logger.warning(f"索引 {index_name} 未找到时间戳字段")
            return None

        except Exception as e:
            self.logger.error(f"获取索引 {index_name} 映射信息失败: {e}")
            return None

    def _get_version_info(self, record: Dict[str, Any]) -> str:
        """获取版本信息"""
        version_fields = ['ne_version', 'version', 'sw_version']
        for field in version_fields:
            if field in record and record[field]:
                return str(record[field])
        return 'unknown'
