"""
数据分析模块
负责分析字段变化趋势并检测异常
"""

import logging
from typing import Dict, List, Any, Tuple, Optional
from datetime import datetime


class DataAnalyzer:
    """数据分析器"""
    
    def __init__(self, config: Dict[str, Any]):
        """初始化数据分析器"""
        self.config = config
        self.threshold_value = config['monitoring']['threshold_value']
        self.logger = self._setup_logger()
        self.anomaly_lists = {}  # 存储异常数据的字典
    
    def _setup_logger(self) -> logging.Logger:
        """设置日志"""
        logger = logging.getLogger('data_analyzer')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def analyze_field_trends(self, index_name: str, field_name: str, data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        分析指定字段的变化趋势
        
        Args:
            index_name: 索引名称
            field_name: 字段名称
            data: 数据列表
            
        Returns:
            超过门限值的异常记录列表
        """
        anomalies = []
        
        if len(data) < 2:
            self.logger.warning(f"数据不足，无法分析 {index_name}.{field_name} 的趋势")
            return anomalies
        
        self.logger.info(f"开始分析 {index_name}.{field_name} 的变化趋势")
        
        for i in range(1, len(data)):
            prev_record = data[i-1]
            curr_record = data[i]
            
            # 获取字段值
            prev_value = self._get_field_value(prev_record, field_name)
            curr_value = self._get_field_value(curr_record, field_name)
            
            if prev_value is None or curr_value is None:
                continue
            
            # 计算变化率
            change_rate = self._calculate_change_rate(prev_value, curr_value)

            # 只记录增长的变化率，且超过门限值
            if change_rate is not None and change_rate > self.threshold_value:
                # 创建异常记录
                anomaly = self._create_anomaly_record(
                    index_name, field_name, prev_record, curr_record, 
                    prev_value, curr_value, change_rate
                )
                anomalies.append(anomaly)
                
                self.logger.warning(
                    f"检测到异常变化: {index_name}.{field_name} "
                    f"从 {prev_value} 变化到 {curr_value} (变化率: {change_rate:.2f}%)"
                )
        
        return anomalies
    
    def _get_field_value(self, record: Dict[str, Any], field_name: str) -> Optional[float]:
        """获取字段值并转换为数值"""
        try:
            value = record.get(field_name)
            if value is None:
                return None

            # 尝试转换为浮点数
            if isinstance(value, (int, float)):
                return float(value)
            elif isinstance(value, str):
                # 处理带单位的值
                value_str = value.strip().upper()

                # 处理存储单位 (G, M, K等)
                if value_str.endswith('G'):
                    return float(value_str[:-1]) * 1024 * 1024 * 1024  # 转换为字节
                elif value_str.endswith('M'):
                    return float(value_str[:-1]) * 1024 * 1024
                elif value_str.endswith('K'):
                    return float(value_str[:-1]) * 1024
                elif value_str.endswith('B'):
                    return float(value_str[:-1])
                else:
                    # 移除其他可能的单位符号
                    value_clean = value_str.replace('%', '').replace(',', '')
                    return float(value_clean)
            else:
                return None

        except (ValueError, TypeError):
            self.logger.warning(f"无法转换字段值 {field_name}: {value}")
            return None
    
    def _calculate_change_rate(self, prev_value: float, curr_value: float) -> Optional[float]:
        """计算变化率（百分比）"""
        try:
            if prev_value == 0:
                # 如果前值为0，使用绝对变化
                return curr_value * 100 if curr_value != 0 else 0
            else:
                return ((curr_value - prev_value) / prev_value) * 100
        except (ZeroDivisionError, TypeError):
            return None
    
    def _create_anomaly_record(self, index_name: str, field_name: str,
                             prev_record: Dict[str, Any], curr_record: Dict[str, Any],
                             prev_value: float, curr_value: float, change_rate: float) -> Dict[str, Any]:
        """创建异常记录"""
        # 合并日期和时间
        start_datetime = f"{prev_record.get('TryDate', '')} {prev_record.get('TryTime', '')}"
        end_datetime = f"{curr_record.get('TryDate', '')} {curr_record.get('TryTime', '')}"

        # 获取版本信息用于标题
        start_version = prev_record.get('Version', '')
        end_version = curr_record.get('Version', '')

        return {
            'Index': index_name,
            'Field': field_name,
            'StartTime': start_datetime.strip(),
            'EndTime': end_datetime.strip(),
            'IP': prev_record.get('IP', ''),  # 应该是同一个IP
            'StartVersion': start_version,
            'EndVersion': end_version,
            'StartValue': prev_value,
            'EndValue': curr_value,
            'ChangeRate': round(change_rate, 2),
            'Threshold': self.threshold_value,
            'VersionInfo': f"{start_version} → {end_version}" if start_version != end_version else start_version
        }
    
    def analyze_all_data(self, all_data: Dict[str, List[Dict[str, Any]]]) -> Dict[str, List[Dict[str, Any]]]:
        """
        分析所有索引和字段的数据，按索引->字段->IP的顺序进行分析

        Args:
            all_data: 所有索引的数据

        Returns:
            按 "索引名-字段名-IP" 分组的异常数据
        """
        self.anomaly_lists = {}

        # 遍历每个索引
        for index_name, data in all_data.items():
            if index_name not in self.config['indices']:
                continue

            # 获取该索引下的所有IP列表
            ip_list = self._get_ip_list_for_index(data)
            self.logger.info(f"索引 {index_name} 包含IP: {', '.join(ip_list)}")

            fields = self.config['indices'][index_name]['fields']

            # 遍历每个字段
            for field_name in fields:
                self.logger.info(f"开始分析索引 {index_name} 字段 {field_name}")

                # 按IP分组该索引的数据
                ip_grouped_data = self._group_index_data_by_ip(data)

                # 遍历每个IP分别进行分析
                for ip in ip_list:
                    if ip not in ip_grouped_data:
                        continue

                    ip_data = ip_grouped_data[ip]
                    list_key = f"{index_name}-{field_name}-{ip}"

                    anomalies = self.analyze_field_trends(index_name, field_name, ip_data)

                    if anomalies:
                        self.anomaly_lists[list_key] = anomalies
                        self.logger.info(f"索引 {index_name} 字段 {field_name} IP {ip} 检测到 {len(anomalies)} 个异常")
                    else:
                        self.logger.info(f"索引 {index_name} 字段 {field_name} IP {ip} 未检测到异常")

        return self.anomaly_lists

    def _get_ip_list_for_index(self, data: List[Dict[str, Any]]) -> List[str]:
        """获取索引数据中的所有IP列表"""
        ip_set = set()
        for record in data:
            ip = record.get('IP', 'unknown')
            if ip != 'unknown':
                ip_set.add(ip)
        return sorted(list(ip_set))

    def _group_index_data_by_ip(self, data: List[Dict[str, Any]]) -> Dict[str, List[Dict[str, Any]]]:
        """按IP分组单个索引的数据"""
        ip_grouped = {}

        for record in data:
            ip = record.get('IP', 'unknown')

            if ip not in ip_grouped:
                ip_grouped[ip] = []

            ip_grouped[ip].append(record)

        # 对每个IP的数据按日期排序
        for ip in ip_grouped:
            ip_grouped[ip].sort(key=lambda x: x.get('TryDate', ''))

        return ip_grouped

    def _group_data_by_ip(self, all_data: Dict[str, List[Dict[str, Any]]]) -> Dict[str, Dict[str, List[Dict[str, Any]]]]:
        """按IP分组数据（保留用于兼容性）"""
        ip_grouped = {}

        for index_name, data in all_data.items():
            for record in data:
                ip = record.get('IP', 'unknown')

                if ip not in ip_grouped:
                    ip_grouped[ip] = {}

                if index_name not in ip_grouped[ip]:
                    ip_grouped[ip][index_name] = []

                ip_grouped[ip][index_name].append(record)

        # 对每个IP的每个索引数据按日期排序
        for ip in ip_grouped:
            for index_name in ip_grouped[ip]:
                ip_grouped[ip][index_name].sort(key=lambda x: x.get('TryDate', ''))

        return ip_grouped
    
    def get_summary_stats(self) -> Dict[str, Any]:
        """获取分析摘要统计"""
        total_anomalies = sum(len(anomalies) for anomalies in self.anomaly_lists.values())
        
        return {
            'total_fields_analyzed': len(self.anomaly_lists),
            'fields_with_anomalies': len([k for k, v in self.anomaly_lists.items() if v]),
            'total_anomalies': total_anomalies,
            'threshold_used': self.threshold_value
        }
