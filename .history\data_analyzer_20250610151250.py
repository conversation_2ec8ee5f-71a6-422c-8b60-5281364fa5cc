"""
数据分析模块
负责分析字段变化趋势并检测异常
"""

import logging
from typing import Dict, List, Any, Tuple, Optional
from datetime import datetime


class DataAnalyzer:
    """数据分析器"""
    
    def __init__(self, config: Dict[str, Any]):
        """初始化数据分析器"""
        self.config = config
        self.threshold_value = config['monitoring']['threshold_value']
        self.logger = self._setup_logger()
        self.anomaly_lists = {}  # 存储异常数据的字典
    
    def _setup_logger(self) -> logging.Logger:
        """设置日志"""
        logger = logging.getLogger('data_analyzer')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def analyze_field_trends(self, index_name: str, field_name: str, data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        分析指定字段的变化趋势
        
        Args:
            index_name: 索引名称
            field_name: 字段名称
            data: 数据列表
            
        Returns:
            超过门限值的异常记录列表
        """
        anomalies = []
        
        if len(data) < 2:
            self.logger.warning(f"数据不足，无法分析 {index_name}.{field_name} 的趋势")
            return anomalies
        
        self.logger.info(f"开始分析 {index_name}.{field_name} 的变化趋势")
        
        for i in range(1, len(data)):
            prev_record = data[i-1]
            curr_record = data[i]
            
            # 获取字段值
            prev_value = self._get_field_value(prev_record, field_name)
            curr_value = self._get_field_value(curr_record, field_name)
            
            if prev_value is None or curr_value is None:
                continue
            
            # 计算变化率
            change_rate = self._calculate_change_rate(prev_value, curr_value)
            
            if change_rate is not None and abs(change_rate) > self.threshold_value:
                # 创建异常记录
                anomaly = self._create_anomaly_record(
                    index_name, field_name, prev_record, curr_record, 
                    prev_value, curr_value, change_rate
                )
                anomalies.append(anomaly)
                
                self.logger.warning(
                    f"检测到异常变化: {index_name}.{field_name} "
                    f"从 {prev_value} 变化到 {curr_value} (变化率: {change_rate:.2f}%)"
                )
        
        return anomalies
    
    def _get_field_value(self, record: Dict[str, Any], field_name: str) -> Optional[float]:
        """获取字段值并转换为数值"""
        try:
            value = record.get(field_name)
            if value is None:
                return None
            
            # 尝试转换为浮点数
            if isinstance(value, (int, float)):
                return float(value)
            elif isinstance(value, str):
                # 移除可能的单位符号
                value_str = value.replace('%', '').replace(',', '').strip()
                return float(value_str)
            else:
                return None
                
        except (ValueError, TypeError):
            self.logger.warning(f"无法转换字段值 {field_name}: {value}")
            return None
    
    def _calculate_change_rate(self, prev_value: float, curr_value: float) -> Optional[float]:
        """计算变化率（百分比）"""
        try:
            if prev_value == 0:
                # 如果前值为0，使用绝对变化
                return curr_value * 100 if curr_value != 0 else 0
            else:
                return ((curr_value - prev_value) / prev_value) * 100
        except (ZeroDivisionError, TypeError):
            return None
    
    def _create_anomaly_record(self, index_name: str, field_name: str, 
                             prev_record: Dict[str, Any], curr_record: Dict[str, Any],
                             prev_value: float, curr_value: float, change_rate: float) -> Dict[str, Any]:
        """创建异常记录"""
        return {
            'Index': index_name,
            'Field': field_name,
            'PrevDate': prev_record.get('TryDate', ''),
            'PrevTime': prev_record.get('TryTime', ''),
            'PrevIP': prev_record.get('IP', ''),
            'PrevVersion': prev_record.get('Version', ''),
            'PrevValue': prev_value,
            'CurrDate': curr_record.get('TryDate', ''),
            'CurrTime': curr_record.get('TryTime', ''),
            'CurrIP': curr_record.get('IP', ''),
            'CurrVersion': curr_record.get('Version', ''),
            'CurrValue': curr_value,
            'ChangeRate': round(change_rate, 2),
            'Threshold': self.threshold_value
        }
    
    def analyze_all_data(self, all_data: Dict[str, List[Dict[str, Any]]]) -> Dict[str, List[Dict[str, Any]]]:
        """
        分析所有索引和字段的数据
        
        Args:
            all_data: 所有索引的数据
            
        Returns:
            按 "索引名-字段名" 分组的异常数据
        """
        self.anomaly_lists = {}
        
        for index_name, data in all_data.items():
            if index_name not in self.config['indices']:
                continue
            
            fields = self.config['indices'][index_name]['fields']
            
            for field_name in fields:
                list_key = f"{index_name}-{field_name}"
                anomalies = self.analyze_field_trends(index_name, field_name, data)
                
                if anomalies:
                    self.anomaly_lists[list_key] = anomalies
                    self.logger.info(f"字段 {list_key} 检测到 {len(anomalies)} 个异常")
                else:
                    self.logger.info(f"字段 {list_key} 未检测到异常")
        
        return self.anomaly_lists
    
    def get_summary_stats(self) -> Dict[str, Any]:
        """获取分析摘要统计"""
        total_anomalies = sum(len(anomalies) for anomalies in self.anomaly_lists.values())
        
        return {
            'total_fields_analyzed': len(self.anomaly_lists),
            'fields_with_anomalies': len([k for k, v in self.anomaly_lists.items() if v]),
            'total_anomalies': total_anomalies,
            'threshold_used': self.threshold_value
        }
